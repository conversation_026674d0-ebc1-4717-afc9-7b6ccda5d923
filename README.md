# Your Layout project

This is codebase for your [Layout app](https://layout.dev/dashboard/projects/$PROJECT_ID).


# Installation

You can always visit your [Layout app](https://layout.dev/dashboard/projects/$PROJECT_ID) and ask the Layout AI agent to handle the updates for you. However, if you'd prefer to update the app manually, follow these steps:

```sh
# 1. Clone the repo.
git clone <$GIT_URL>

# 2. Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# 3. Install the dependencies.
npm install
# You can use `bun install` but need to run `npm install -g bun` to install it first.

# 4. Start the development server with auto-reloading and an instant preview.
npm run dev
# You can use `bun run dev`

# 5. On your browser navigate to http://localhost:5173/
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with Vite, React, Shadcn, Tailwind CSS, React Router. For a complete list see `package.json`.

## How can I deploy this project?

Simply open [Layout app](https://layout.dev/projects/$PROJECT_ID) and publish the app from there.

## I want to use a custom domain, is that possible?

We don't support custom domains yet.
